#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合型质控规则分类测试
测试三元分类系统（规则、内涵、规则和内涵）
"""

import sys
import os

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_document_standards import (
    classify_rule_by_standards,
    is_structural_issue,
    is_content_issue,
    is_mixed_type_issue
)

def test_mixed_type_detection():
    """测试混合型规则检测"""
    print("🔬 混合型质控规则检测测试")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "典型混合型案例",
            "rule_content": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等",
            "expected_mixed": True,
            "expected_structural": True,
            "expected_content": True,
            "reason": "同时包含章节缺失（缺出院医嘱）和具体内容要求（药名、剂量等）"
        },
        {
            "name": "另一个混合型案例",
            "rule_content": "缺诊疗计划，未写明具体治疗方案、用药指导、复查安排等",
            "expected_mixed": True,
            "expected_structural": True,
            "expected_content": True,
            "reason": "章节缺失+具体内容要求"
        },
        {
            "name": "纯规则质控案例",
            "rule_content": "首次病程缺病例特点",
            "expected_mixed": False,
            "expected_structural": True,
            "expected_content": False,
            "reason": "仅涉及章节缺失"
        },
        {
            "name": "纯内涵质控案例",
            "rule_content": "主诉描述有缺陷",
            "expected_mixed": False,
            "expected_structural": False,
            "expected_content": True,
            "reason": "仅涉及内容质量问题"
        },
        {
            "name": "复杂混合型案例",
            "rule_content": "缺手术记录，手术过程未详细记录手术步骤、出血量、麻醉方式等关键信息",
            "expected_mixed": True,
            "expected_structural": True,
            "expected_content": True,
            "reason": "章节缺失+详细内容要求"
        }
    ]
    
    correct_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print("-" * 50)
        
        rule_content = case['rule_content']
        rule_type = "段落完整性"  # 默认规则类型
        
        # 执行各种检测
        is_mixed = is_mixed_type_issue(rule_content, rule_type)
        is_structural = is_structural_issue(rule_content, rule_type)
        is_content = is_content_issue(rule_content, rule_type)
        
        # 检查结果
        mixed_correct = is_mixed == case['expected_mixed']
        structural_correct = is_structural == case['expected_structural']
        content_correct = is_content == case['expected_content']
        
        all_correct = mixed_correct and structural_correct and content_correct
        if all_correct:
            correct_count += 1
        
        print(f"📋 规则内容: {rule_content}")
        print(f"{'✅' if mixed_correct else '❌'} 混合型检测: {is_mixed} (期望: {case['expected_mixed']})")
        print(f"{'✅' if structural_correct else '❌'} 结构性检测: {is_structural} (期望: {case['expected_structural']})")
        print(f"{'✅' if content_correct else '❌'} 内容性检测: {is_content} (期望: {case['expected_content']})")
        print(f"💡 分析依据: {case['reason']}")
    
    print(f"\n📊 检测准确率: {correct_count}/{total_count} ({correct_count/total_count*100:.1f}%)")
    return correct_count == total_count

def test_three_way_classification():
    """测试三元分类系统"""
    print(f"\n" + "=" * 60)
    print("🎯 三元分类系统测试")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "混合型规则1",
            "rule": {
                "rule_content": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "出院记录"
            },
            "expected": "规则和内涵",
            "reason": "同时包含章节缺失和具体内容要求"
        },
        {
            "name": "混合型规则2",
            "rule": {
                "rule_content": "缺诊疗计划，未写明具体治疗方案、用药指导、复查安排等",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录"
            },
            "expected": "规则和内涵",
            "reason": "章节缺失+具体内容要求"
        },
        {
            "name": "纯规则质控",
            "rule": {
                "rule_content": "首次病程缺病例特点",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录"
            },
            "expected": "规则",
            "reason": "仅涉及章节缺失"
        },
        {
            "name": "纯内涵质控",
            "rule": {
                "rule_content": "主诉描述有缺陷",
                "rule_type_chinese": "内容完整性",
                "document_type_chinese": "入院记录"
            },
            "expected": "内涵",
            "reason": "仅涉及内容质量问题"
        },
        {
            "name": "时效性规则",
            "rule": {
                "rule_content": "入院记录未在24小时内完成",
                "rule_type_chinese": "时效性",
                "document_type_chinese": "入院记录"
            },
            "expected": "规则",
            "reason": "时效性问题属于规则质控"
        },
        {
            "name": "复杂混合型",
            "rule": {
                "rule_content": "缺手术记录，手术过程未详细记录手术步骤、出血量、麻醉方式等关键信息",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "手术记录"
            },
            "expected": "规则和内涵",
            "reason": "章节缺失+详细内容要求"
        }
    ]
    
    correct_count = 0
    total_count = len(test_cases)
    classification_stats = {"规则": 0, "内涵": 0, "规则和内涵": 0}
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print("-" * 40)
        
        rule = case['rule']
        classification = classify_rule_by_standards(rule)
        
        is_correct = classification == case['expected']
        status = "✅" if is_correct else "❌"
        
        if is_correct:
            correct_count += 1
        
        classification_stats[classification] += 1
        
        # 显示结果
        icon = "🔧" if classification == "规则" else "📋" if classification == "内涵" else "⚖️"
        print(f"📋 规则内容: {rule['rule_content'][:60]}...")
        print(f"{status} {icon} 分类结果: {classification} (期望: {case['expected']})")
        print(f"💡 分析依据: {case['reason']}")
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📊 三元分类测试结果")
    print("=" * 60)
    print(f"测试案例总数: {total_count}")
    print(f"正确分类数量: {correct_count}")
    print(f"分类准确率: {correct_count/total_count*100:.1f}%")
    
    print(f"\n分类分布:")
    for class_type, count in classification_stats.items():
        percentage = count / total_count * 100
        print(f"  {class_type}: {count} 条 ({percentage:.1f}%)")
    
    return correct_count == total_count

def test_real_mixed_cases():
    """测试真实的混合型案例"""
    print(f"\n" + "=" * 60)
    print("📋 真实混合型案例测试")
    print("=" * 60)
    
    # 从实际数据中找到的可能的混合型案例
    real_cases = [
        "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等",
        "缺诊疗计划，未明确具体治疗方案和用药指导",
        "缺手术记录，手术过程描述不详细",
        "缺病例特点，临床表现分析不充分"
    ]
    
    for i, rule_content in enumerate(real_cases, 1):
        rule_data = {
            "rule_content": rule_content,
            "rule_type_chinese": "段落完整性",
            "document_type_chinese": "出院记录"
        }
        
        classification = classify_rule_by_standards(rule_data)
        icon = "🔧" if classification == "规则" else "📋" if classification == "内涵" else "⚖️"
        
        print(f"{i}. {icon} {rule_content}")
        print(f"   分类结果: {classification}")
        print()

def main():
    """主测试函数"""
    try:
        print("🎯 混合型质控规则分类系统测试")
        print("=" * 60)
        
        # 执行各项测试
        detection_success = test_mixed_type_detection()
        classification_success = test_three_way_classification()
        test_real_mixed_cases()
        
        print(f"\n" + "=" * 60)
        if detection_success and classification_success:
            print("✅ 混合型分类系统测试全部通过！")
            print("🎉 三元分类系统（规则、内涵、规则和内涵）工作正常")
        else:
            print("❌ 部分测试未通过，需要进一步调整")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
