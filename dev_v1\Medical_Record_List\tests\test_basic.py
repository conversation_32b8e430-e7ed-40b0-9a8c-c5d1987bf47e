# -*- coding: utf-8 -*-
"""
基础测试医疗记录列表生成器 - 不进行翻译，只测试数据读取和统计
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator

def test_basic_functionality():
    """测试基础功能"""
    print("开始测试医疗记录列表生成器基础功能...")
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # Step 1: 读取Excel文件
    print("\n=== Step 1: 读取Excel文件 ===")
    if not generator.read_excel_file(excel_path):
        print("读取Excel文件失败")
        return False
    
    # Step 2: 验证并提取数据
    print("\n=== Step 2: 验证并提取数据 ===")
    if not generator.validate_and_extract_data():
        print("数据验证和提取失败")
        return False
    
    # Step 3: 生成类别统计
    print("\n=== Step 3: 生成类别统计 ===")
    if not generator.generate_category_statistics():
        print("类别统计生成失败")
        return False
    
    # 显示统计结果
    print("\n=== 统计结果 ===")
    print(f"总记录数: {len(generator.processed_data)}")
    
    print("\n类别统计:")
    for category, stats in generator.category_stats.items():
        print(f"  {category}: {stats['count']} 个唯一值")
        print(f"    前5个值: {stats['unique_values'][:5]}")
    
    # 显示文书类型分布
    doc_type_count = {}
    for record in generator.processed_data:
        doc_type = str(record.get('文书类型', '')).strip()
        if doc_type and doc_type != 'nan':
            doc_type_count[doc_type] = doc_type_count.get(doc_type, 0) + 1
    
    print(f"\n文书类型分布 (共{len(doc_type_count)}种):")
    sorted_doc_types = sorted(doc_type_count.items(), key=lambda x: x[1], reverse=True)
    for doc_type, count in sorted_doc_types[:10]:
        print(f"  {doc_type}: {count} 条记录")
    if len(sorted_doc_types) > 10:
        print(f"  ... 还有 {len(sorted_doc_types) - 10} 种文书类型")
    
    print("\n基础功能测试完成！")
    return True

if __name__ == "__main__":
    test_basic_functionality()
