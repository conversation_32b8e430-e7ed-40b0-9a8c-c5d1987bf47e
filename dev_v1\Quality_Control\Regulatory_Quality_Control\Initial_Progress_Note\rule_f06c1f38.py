"""
规则ID：rule_f06c1f38
规则描述：首次病程缺诊疗计划
"""

def check_rule(medical_record):
    """
    检查首次病程记录中是否缺少诊疗计划
    返回True表示存在问题（缺少），False表示正常
    """
    try:
        # 假设medical_record是包含完整病历文本的字符串
        # 检查是否包含诊疗计划相关关键词
        if "诊疗计划" not in medical_record:
            return True
        return False
    except Exception as e:
        # 捕获任何异常并返回错误状态
        print(f"Error processing medical record: {e}")
        return True

if __name__ == "__main__":
    # 测试用例1：正常情况包含诊疗计划
    test_record1 = "患者入院后，制定诊疗计划如下：1. 抗感染治疗 2. 对症支持治疗"
    assert check_rule(test_record1) == False

    # 测试用例2：缺少诊疗计划
    test_record2 = "患者入院后，目前病情稳定。"
    assert check_rule(test_record2) == True

    # 测试用例3：异常输入None
    test_record3 = None
    assert check_rule(test_record3) == True

    # 测试用例4：包含其他变体（虽然规则要求特定关键词）
    test_record4 = "已制定治疗方案和护理计划"
    assert check_rule(test_record4) == True

    print("All tests passed.")