# -*- coding: utf-8 -*-
"""
医疗质控API服务
Flask Web应用，提供标准化质控功能的HTTP接口
"""
import json
import sys
import traceback
from pathlib import Path
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# 添加质控模块路径
sys.path.append(str(Path(__file__).parent / "Quality_Control"))

try:
    from Quality_Control.quality_control_main import QualityControlMain
except ImportError as e:
    print(f"导入质控模块失败: {e}")
    QualityControlMain = None

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用CORS支持

# 配置
app.config['JSON_AS_ASCII'] = False  # 支持中文JSON响应
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True  # 美化JSON输出

# 全局质控实例
qc_main = None

def init_quality_control():
    """初始化质控系统"""
    global qc_main
    try:
        if QualityControlMain is None:
            return False, "质控模块导入失败"
        
        qc_main = QualityControlMain()
        return True, "质控系统初始化成功"
    except Exception as e:
        return False, f"质控系统初始化失败: {str(e)}"

def validate_request_data(data):
    """
    验证请求数据格式
    
    Args:
        data (dict): 请求数据
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not isinstance(data, dict):
        return False, "请求数据必须是JSON对象"
    
    # 检查必需字段
    if 'type' not in data:
        return False, "缺少必需参数: type"
    
    if 'data' not in data:
        return False, "缺少必需参数: data"
    
    # 验证type参数
    valid_types = ["Discharge Summary", "Initial Progress Note"]
    if data['type'] not in valid_types:
        return False, f"不支持的文档类型: {data['type']}，支持的类型: {valid_types}"
    
    # 验证data参数
    if not isinstance(data['data'], dict):
        return False, "data参数必须是JSON对象"
    
    return True, None

def create_error_response(error_message, status_code=400, error_type="ValidationError"):
    """
    创建错误响应
    
    Args:
        error_message (str): 错误信息
        status_code (int): HTTP状态码
        error_type (str): 错误类型
        
    Returns:
        tuple: (response, status_code)
    """
    response = {
        "success": False,
        "error": {
            "type": error_type,
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
    }
    return jsonify(response), status_code

def create_success_response(data):
    """
    创建成功响应
    
    Args:
        data (dict): 响应数据
        
    Returns:
        dict: 成功响应
    """
    response = {
        "success": True,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response)

@app.route('/hospical', methods=['GET'])
def index():
    """API首页"""
    return jsonify({
        "service": "医疗质控API服务",
        "version": "1.0.0",
        "description": "提供标准化医疗文档质控功能",
        "endpoints": {
            "quality_control": {
                "path": "/api/quality_control",
                "method": "POST",
                "description": "执行医疗文档质控检查"
            },
            "health": {
                "path": "/api/health",
                "method": "GET",
                "description": "服务健康检查"
            },
            "docs": {
                "path": "/api/docs",
                "method": "GET",
                "description": "API文档"
            }
        }
    })

@app.route('/hospical/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    global qc_main
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "flask_app": "running",
            "quality_control_system": "unknown"
        }
    }
    
    # 检查质控系统状态
    if qc_main is None:
        success, message = init_quality_control()
        if success:
            health_status["components"]["quality_control_system"] = "running"
        else:
            health_status["status"] = "degraded"
            health_status["components"]["quality_control_system"] = f"error: {message}"
    else:
        health_status["components"]["quality_control_system"] = "running"
    
    status_code = 200 if health_status["status"] == "healthy" else 503
    return jsonify(health_status), status_code

@app.route('/hospical/quality_control', methods=['POST'])
def quality_control():
    """
    医疗文档质控接口
    
    请求格式:
    {
        "type": "Discharge Summary" | "Initial Progress Note",
        "data": {
            "Patient": {...},
            "VisitInfo": {...},
            "RepSummaryInfo": {...} | "CaseHistoryInfo": {...}
        }
    }
    
    响应格式:
    {
        "success": true,
        "data": {
            "quality_score": {...},
            "quality_status": "通过" | "不通过",
            "quality_issues": {...},
            "quality_suggestions": {...},
            "statistics": {...},
            "metadata": {...}
        },
        "timestamp": "2025-08-05T10:30:00"
    }
    """
    global qc_main
    
    try:
        # 初始化质控系统（如果需要）
        if qc_main is None:
            success, message = init_quality_control()
            if not success:
                return create_error_response(
                    f"质控系统初始化失败: {message}",
                    status_code=500,
                    error_type="SystemError"
                )
        
        # 获取请求数据
        if not request.is_json:
            return create_error_response("请求必须是JSON格式")
        
        request_data = request.get_json()
        if request_data is None:
            return create_error_response("无效的JSON数据")
        
        # 验证请求数据
        is_valid, error_message = validate_request_data(request_data)
        if not is_valid:
            return create_error_response(error_message)
        
        # 提取参数
        doc_type = request_data['type']
        medical_data = request_data['data']
        
        # 执行质控
        result = qc_main.run_standardized_quality_control(doc_type, medical_data)
        
        # 检查质控结果
        if "error" in result:
            return create_error_response(
                result["error"],
                status_code=400,
                error_type="QualityControlError"
            )
        
        # 返回成功响应
        return create_success_response(result)
        
    except Exception as e:
        # 记录详细错误信息
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc()
        }
        
        print(f"质控API错误: {error_details}")
        
        return create_error_response(
            f"服务器内部错误: {str(e)}",
            status_code=500,
            error_type="InternalServerError"
        )

@app.route('/hospical/docs', methods=['GET'])
def api_docs():
    """API文档"""
    docs = {
        "title": "医疗质控API文档",
        "version": "1.0.0",
        "description": "提供标准化医疗文档质控功能的RESTful API",
        "base_url": request.host_url.rstrip('/'),
        "endpoints": {
            "/api/quality_control": {
                "method": "POST",
                "description": "执行医疗文档质控检查",
                "content_type": "application/json",
                "parameters": {
                    "type": {
                        "type": "string",
                        "required": True,
                        "description": "文档类型",
                        "enum": ["Discharge Summary", "Initial Progress Note"]
                    },
                    "data": {
                        "type": "object",
                        "required": True,
                        "description": "医疗记录数据，必须包含Patient、VisitInfo和RepSummaryInfo/CaseHistoryInfo"
                    }
                },
                "example_request": {
                    "type": "Discharge Summary",
                    "data": {
                        "Patient": {
                            "PatientId": "P001",
                            "PatientName": "张三",
                            "Age": 45,
                            "SexName": "男"
                        },
                        "VisitInfo": {
                            "AdmissionNumber": "A001",
                            "DeptName": "内科"
                        },
                        "RepSummaryInfo": {
                            "AssayResult": "血常规正常",
                            "IsTumor": "否",
                            "TreatmentOutcomes": "好转"
                        }
                    }
                },
                "response": {
                    "success_example": {
                        "success": True,
                        "data": {
                            "quality_score": {
                                "total_score": 85,
                                "base_score": 100,
                                "total_deductions": 15
                            },
                            "quality_status": "通过",
                            "quality_issues": {},
                            "quality_suggestions": {},
                            "statistics": {}
                        }
                    },
                    "error_example": {
                        "success": False,
                        "error": {
                            "type": "ValidationError",
                            "message": "缺少必需参数: type"
                        }
                    }
                }
            }
        }
    }
    return jsonify(docs)

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return create_error_response(
        "请求的资源不存在",
        status_code=404,
        error_type="NotFoundError"
    )

@app.errorhandler(405)
def method_not_allowed(error):
    """405错误处理"""
    return create_error_response(
        "请求方法不被允许",
        status_code=405,
        error_type="MethodNotAllowedError"
    )

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return create_error_response(
        "服务器内部错误",
        status_code=500,
        error_type="InternalServerError"
    )

if __name__ == '__main__':
    print("=" * 60)
    print("医疗质控API服务启动")
    print("=" * 60)
    
    # 初始化质控系统
    success, message = init_quality_control()
    print(f"质控系统初始化: {message}")
    
    # 启动Flask应用
    print("\n🚀 启动Flask服务...")
    print("📍 API地址: http://localhost:9086/hospical")
    print("📖 API文档: http://localhost:9086/hospical/docs")
    print("🏥 质控接口: http://localhost:9086/hospical/quality_control")
    print("💚 健康检查: http://localhost:9086/hospical/health")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 60)
    
    app.run(
        host='0.0.0.0',
        port=9086,
        debug=True,
        threaded=True
    )
