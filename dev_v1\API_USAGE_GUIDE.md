# 医疗质控API使用指南

## 概述

本文档介绍如何使用Flask Web应用提供的医疗质控API服务。该API基于 `dev_v1/Quality_Control/quality_control_main.py` 中的标准化质控功能，提供HTTP接口供外部系统调用。

## 快速开始

### 1. 安装依赖

```bash
cd dev_v1
pip install -r requirements.txt
```

### 2. 启动API服务

```bash
python quality_control_api.py
```

服务启动后将在 `http://localhost:5000` 提供API服务。

### 3. 验证服务状态

```bash
curl http://localhost:5000/api/health
```

## API接口

### 基础信息

- **基础URL**: `http://localhost:5000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 接口列表

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/` | GET | API首页和服务信息 |
| `/api/health` | GET | 服务健康检查 |
| `/api/docs` | GET | API文档 |
| `/api/quality_control` | POST | 医疗文档质控检查 |

## 核心接口详解

### POST /api/quality_control

执行医疗文档质控检查的核心接口。

#### 请求格式

```json
{
  "type": "Discharge Summary" | "Initial Progress Note",
  "data": {
    "Patient": {
      "PatientId": "string",
      "PatientName": "string",
      "Age": number,
      "SexName": "string"
    },
    "VisitInfo": {
      "AdmissionNumber": "string",
      "DeptName": "string",
      ...
    },
    "RepSummaryInfo": { ... } | "CaseHistoryInfo": { ... }
  }
}
```

#### 参数说明

**type** (string, 必需)
- `"Discharge Summary"`: 处理出院小结数据
- `"Initial Progress Note"`: 处理首次病程记录数据

**data** (object, 必需)
- 医疗记录数据，必须严格遵循测试数据集的JSON结构
- 出院小结：包含 `Patient`、`VisitInfo`、`RepSummaryInfo`
- 首次病程记录：包含 `Patient`、`VisitInfo`、`CaseHistoryInfo`

#### 响应格式

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "quality_score": {
      "total_score": 85,
      "base_score": 100,
      "total_deductions": 15,
      "score_details": {
        "规则名称1": "扣分: 5分",
        "规则名称2": "扣分: 0分"
      }
    },
    "quality_status": "通过",
    "quality_issues": {
      "规则名称1": "缺失主要化验结果",
      "规则名称2": "无"
    },
    "quality_suggestions": {
      "规则名称1": "请补充主要化验结果",
      "规则名称2": "无"
    },
    "statistics": {
      "total_rules": 10,
      "regulatory_rules": 6,
      "connotation_rules": 4,
      "total_problems": 2,
      "pass_threshold": 60,
      "quality_grade": "良好"
    },
    "metadata": {
      "document_type_chinese": "出院记录",
      "document_type_english": "Discharge Summary",
      "timestamp": "2025-08-05T10:30:00"
    }
  },
  "timestamp": "2025-08-05T10:30:00"
}
```

**错误响应 (400/500)**:
```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "缺少必需参数: type",
    "timestamp": "2025-08-05T10:30:00"
  }
}
```

## 使用示例

### Python示例

```python
import requests
import json

# API配置
API_URL = "http://localhost:5000/api/quality_control"

# 出院小结数据示例
discharge_data = {
    "type": "Discharge Summary",
    "data": {
        "Patient": {
            "PatientId": "P001",
            "PatientName": "张三",
            "Age": 45,
            "SexName": "男"
        },
        "VisitInfo": {
            "AdmissionNumber": "A001",
            "DeptName": "内科",
            "AttendingDocName": "李医生",
            "BedNurName": "王护士",
            "ChiefDocName": "陈主任"
        },
        "RepSummaryInfo": {
            "AssayResult": "血常规正常，肝功能正常",
            "IsTumor": "否",
            "TreatmentOutcomes": "好转",
            "Complication": "无",
            "DisHospitalDisease": "患者一般情况良好",
            "DisHospitalDrugs": "口服药物治疗"
        }
    }
}

# 发送请求
try:
    response = requests.post(
        API_URL,
        json=discharge_data,
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            data = result['data']
            print(f"质控分数: {data['quality_score']['total_score']}/100")
            print(f"质控状态: {data['quality_status']}")
            print(f"质量等级: {data['statistics']['quality_grade']}")
        else:
            print(f"质控失败: {result['error']['message']}")
    else:
        print(f"请求失败: {response.status_code}")
        
except requests.exceptions.RequestException as e:
    print(f"连接失败: {e}")
```

### JavaScript示例

```javascript
// 出院小结数据示例
const dischargeData = {
    type: "Discharge Summary",
    data: {
        Patient: {
            PatientId: "P001",
            PatientName: "张三",
            Age: 45,
            SexName: "男"
        },
        VisitInfo: {
            AdmissionNumber: "A001",
            DeptName: "内科",
            AttendingDocName: "李医生",
            BedNurName: "王护士",
            ChiefDocName: "陈主任"
        },
        RepSummaryInfo: {
            AssayResult: "血常规正常，肝功能正常",
            IsTumor: "否",
            TreatmentOutcomes: "好转",
            Complication: "无",
            DisHospitalDisease: "患者一般情况良好",
            DisHospitalDrugs: "口服药物治疗"
        }
    }
};

// 发送请求
fetch('http://localhost:5000/api/quality_control', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(dischargeData)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        const data = result.data;
        console.log(`质控分数: ${data.quality_score.total_score}/100`);
        console.log(`质控状态: ${data.quality_status}`);
        console.log(`质量等级: ${data.statistics.quality_grade}`);
    } else {
        console.error(`质控失败: ${result.error.message}`);
    }
})
.catch(error => {
    console.error('连接失败:', error);
});
```

### cURL示例

```bash
# 出院小结质控
curl -X POST http://localhost:5000/api/quality_control \
  -H "Content-Type: application/json" \
  -d '{
    "type": "Discharge Summary",
    "data": {
      "Patient": {
        "PatientId": "P001",
        "PatientName": "张三",
        "Age": 45,
        "SexName": "男"
      },
      "VisitInfo": {
        "AdmissionNumber": "A001",
        "DeptName": "内科"
      },
      "RepSummaryInfo": {
        "AssayResult": "血常规正常",
        "IsTumor": "否",
        "TreatmentOutcomes": "好转"
      }
    }
  }'
```

## 测试工具

### 自动化测试

运行完整的API测试套件：

```bash
python test_api.py
```

测试包括：
- ✅ 健康检查接口
- ✅ API文档接口
- ✅ 出院小结质控
- ✅ 首次病程记录质控
- ✅ 错误处理机制
- ✅ 性能测试

### 手动测试

1. **健康检查**:
   ```bash
   curl http://localhost:5000/api/health
   ```

2. **API文档**:
   ```bash
   curl http://localhost:5000/api/docs
   ```

3. **质控测试**:
   使用测试数据文件中的数据进行测试

## 错误处理

### 常见错误类型

| 错误类型 | HTTP状态码 | 描述 |
|---------|-----------|------|
| ValidationError | 400 | 请求参数验证失败 |
| QualityControlError | 400 | 质控执行失败 |
| SystemError | 500 | 系统初始化失败 |
| InternalServerError | 500 | 服务器内部错误 |
| NotFoundError | 404 | 资源不存在 |
| MethodNotAllowedError | 405 | 请求方法不允许 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "不支持的文档类型: Invalid Type，支持的类型: ['Discharge Summary', 'Initial Progress Note']",
    "timestamp": "2025-08-05T10:30:00"
  }
}
```

## 性能说明

- **响应时间**: 通常在1-3秒内完成质控检查
- **并发支持**: 支持多线程并发请求
- **超时设置**: 建议客户端设置30秒超时
- **内存使用**: 每次请求约占用50-100MB内存

## 部署建议

### 开发环境
```bash
python quality_control_api.py
```

### 生产环境
建议使用WSGI服务器如Gunicorn：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 quality_control_api:app
```

### Docker部署
可以创建Dockerfile进行容器化部署。

## 注意事项

1. **数据格式**: 严格按照测试数据集的JSON结构提供数据
2. **字符编码**: 使用UTF-8编码处理中文内容
3. **超时设置**: 质控处理可能需要较长时间，建议设置适当的超时
4. **错误处理**: 始终检查响应中的success字段
5. **安全性**: 生产环境中应添加认证和授权机制

## 技术支持

如有问题，请检查：
1. API服务是否正常启动
2. 请求数据格式是否正确
3. 网络连接是否正常
4. 服务器日志中的错误信息
