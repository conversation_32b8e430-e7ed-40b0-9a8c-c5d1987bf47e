#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三元分类系统演示
展示规则、内涵、规则和内涵三种分类类型
"""

import sys
import os

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_document_standards import classify_rule_by_standards

def demo_three_way_classification():
    """演示三元分类功能"""
    print("🏥 医疗记录质量控制三元分类系统演示")
    print("=" * 60)
    
    # 演示案例
    demo_cases = [
        {
            "category": "🔧 规则质控案例",
            "cases": [
                {
                    "name": "章节缺失",
                    "rule": {
                        "rule_content": "首次病程缺病例特点",
                        "rule_type_chinese": "段落完整性",
                        "document_type_chinese": "首次病程记录"
                    },
                    "reason": "整个章节缺失，属于结构完整性问题"
                },
                {
                    "name": "时效性问题",
                    "rule": {
                        "rule_content": "入院记录未在24小时内完成",
                        "rule_type_chinese": "时效性",
                        "document_type_chinese": "入院记录"
                    },
                    "reason": "时效性问题属于规则质控"
                },
                {
                    "name": "条件性章节缺失",
                    "rule": {
                        "rule_content": "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断",
                        "rule_type_chinese": "段落完整性",
                        "document_type_chinese": "首次病程记录"
                    },
                    "reason": "即使有条件限制，章节缺失仍属于结构性问题"
                }
            ]
        },
        {
            "category": "📋 内涵质控案例",
            "cases": [
                {
                    "name": "内容质量问题",
                    "rule": {
                        "rule_content": "主诉描述有缺陷",
                        "rule_type_chinese": "内容完整性",
                        "document_type_chinese": "入院记录"
                    },
                    "reason": "章节存在但内容质量不达标"
                },
                {
                    "name": "内容不充分",
                    "rule": {
                        "rule_content": "现病史缺发病情况",
                        "rule_type_chinese": "内容完整性",
                        "document_type_chinese": "入院记录"
                    },
                    "reason": "章节存在但具体内容要素缺失"
                },
                {
                    "name": "数据一致性",
                    "rule": {
                        "rule_content": "现病史主要症状或体征、症状性质、持续时间与主诉不符",
                        "rule_type_chinese": "数据一致性",
                        "document_type_chinese": "入院记录"
                    },
                    "reason": "数据逻辑一致性问题"
                }
            ]
        },
        {
            "category": "⚖️ 混合质控案例（规则和内涵）",
            "cases": [
                {
                    "name": "典型混合型",
                    "rule": {
                        "rule_content": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等",
                        "rule_type_chinese": "段落完整性",
                        "document_type_chinese": "出院记录"
                    },
                    "reason": "同时包含章节缺失（缺出院医嘱）和具体内容要求（药名、剂量等）"
                },
                {
                    "name": "复杂混合型",
                    "rule": {
                        "rule_content": "缺手术记录，手术过程未详细记录手术步骤、出血量、麻醉方式等关键信息",
                        "rule_type_chinese": "段落完整性",
                        "document_type_chinese": "手术记录"
                    },
                    "reason": "章节缺失+详细内容要求"
                },
                {
                    "name": "诊疗计划混合型",
                    "rule": {
                        "rule_content": "缺诊疗计划，未写明具体治疗方案、用药指导、复查安排等",
                        "rule_type_chinese": "段落完整性",
                        "document_type_chinese": "首次病程记录"
                    },
                    "reason": "章节缺失+具体内容要求"
                }
            ]
        }
    ]
    
    total_cases = 0
    correct_cases = 0
    classification_stats = {"规则": 0, "内涵": 0, "规则和内涵": 0}
    
    for category_data in demo_cases:
        print(f"\n{category_data['category']}")
        print("-" * 50)
        
        for i, case in enumerate(category_data['cases'], 1):
            rule = case['rule']
            classification = classify_rule_by_standards(rule)
            
            # 统计
            total_cases += 1
            classification_stats[classification] += 1
            
            # 判断是否正确（基于类别推断期望结果）
            if "规则质控" in category_data['category']:
                expected = "规则"
            elif "内涵质控" in category_data['category']:
                expected = "内涵"
            else:  # 混合质控
                expected = "规则和内涵"
            
            is_correct = classification == expected
            if is_correct:
                correct_cases += 1
            
            status = "✅" if is_correct else "❌"
            icon = "🔧" if classification == "规则" else "📋" if classification == "内涵" else "⚖️"
            
            print(f"\n{i}. {case['name']}")
            print(f"   📋 规则内容: {rule['rule_content'][:60]}...")
            print(f"   {status} {icon} 分类结果: {classification}")
            print(f"   💡 分析依据: {case['reason']}")
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📊 三元分类系统演示总结")
    print("=" * 60)
    print(f"演示案例总数: {total_cases}")
    print(f"正确分类数量: {correct_cases}")
    print(f"分类准确率: {correct_cases/total_cases*100:.1f}%")
    
    print(f"\n分类分布:")
    for class_type, count in classification_stats.items():
        percentage = count / total_cases * 100
        icon = "🔧" if class_type == "规则" else "📋" if class_type == "内涵" else "⚖️"
        print(f"  {icon} {class_type}: {count} 条 ({percentage:.1f}%)")

def demo_classification_logic():
    """演示分类逻辑"""
    print(f"\n" + "=" * 60)
    print("🧠 三元分类逻辑演示")
    print("=" * 60)
    
    print("分类决策树：")
    print("1. 🔍 检查是否为混合型问题（同时包含章节缺失和具体内容要求）→ 规则和内涵")
    print("2. 🔍 检查是否为结构层面问题（整个章节缺失）→ 规则质控")
    print("3. 🔍 检查是否为时效性、签名、格式问题 → 规则质控")
    print("4. 🔍 检查是否为内容充分性、医疗质量问题 → 内涵质控")
    
    print(f"\n关键识别特征：")
    print("🔧 规则质控特征：")
    print("   - 缺XX（整个章节缺失）")
    print("   - 未在XX小时内完成（时效性）")
    print("   - 签名问题、格式问题")
    
    print("📋 内涵质控特征：")
    print("   - XX描述有缺陷（内容质量）")
    print("   - 缺XX情况（具体内容要素）")
    print("   - XX不符、不一致（数据一致性）")
    
    print("⚖️ 混合质控特征：")
    print("   - 缺XX，XX未写明...（章节缺失+内容要求）")
    print("   - 缺XX记录，XX过程未详细...（结构+内容）")

def interactive_classification():
    """交互式分类演示"""
    print(f"\n" + "=" * 60)
    print("🎯 交互式三元分类演示")
    print("=" * 60)
    
    print("请输入规则信息进行三元分类测试：")
    
    while True:
        try:
            print("\n" + "-" * 40)
            rule_content = input("规则内容 (输入 'quit' 退出): ").strip()
            
            if rule_content.lower() == 'quit':
                break
                
            if not rule_content:
                print("❌ 规则内容不能为空")
                continue
            
            rule_type = input("规则类型 (如：段落完整性、内容完整性、时效性): ").strip()
            doc_type = input("文档类型 (如：首次病程记录、入院记录): ").strip()
            
            # 构建规则数据
            rule_data = {
                "rule_content": rule_content,
                "rule_type_chinese": rule_type or "未知",
                "document_type_chinese": doc_type or "未知"
            }
            
            # 执行分类
            classification = classify_rule_by_standards(rule_data)
            
            # 显示结果
            icon = "🔧" if classification == "规则" else "📋" if classification == "内涵" else "⚖️"
            print(f"\n{icon} 分类结果: {classification}")
            
            if classification == "规则":
                print("💡 这是一个结构完整性问题（章节缺失、时效性或格式问题）")
            elif classification == "内涵":
                print("💡 这是一个内容充分性问题（内容质量、完整性或一致性问题）")
            else:
                print("💡 这是一个混合型问题（同时包含结构性和内容性要素）")
                
        except KeyboardInterrupt:
            print("\n\n👋 演示结束")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主演示函数"""
    try:
        demo_three_way_classification()
        demo_classification_logic()
        
        print(f"\n" + "=" * 60)
        print("✅ 三元分类系统演示完成！")
        
        # 询问是否进行交互式演示
        response = input("\n是否进行交互式分类测试？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            interactive_classification()
        
        print("\n👋 感谢使用医疗记录质量控制三元分类系统！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
