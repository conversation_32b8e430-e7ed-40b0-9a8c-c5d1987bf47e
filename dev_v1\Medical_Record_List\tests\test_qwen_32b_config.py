# -*- coding: utf-8 -*-
"""
测试 qwen_32B_config 配置
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def test_qwen_32b_config():
    """测试 qwen_32B_config 配置"""
    print("=" * 60)
    print("测试 qwen_32B_config 配置")
    print("=" * 60)
    
    try:
        # 导入配置
        from config import qwen_32B_config
        
        print("✅ qwen_32B_config 导入成功")
        print(f"模型名称: {qwen_32B_config.get('model')}")
        print(f"请求URL: {qwen_32B_config.get('request_url')}")
        print(f"API密钥: {qwen_32B_config.get('api_key')[:20]}..." if qwen_32B_config.get('api_key') else "未设置")
        print(f"最大令牌数: {qwen_32B_config.get('default_max_tokens')}")
        print(f"温度: {qwen_32B_config.get('default_temperature')}")
        print(f"Top-p: {qwen_32B_config.get('default_top_p')}")
        
        # 验证必需的配置项
        required_keys = ['model', 'request_url', 'api_key', 'headers']
        missing_keys = []
        
        for key in required_keys:
            if key not in qwen_32B_config:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 缺少必需的配置项: {missing_keys}")
            return False
        else:
            print("✅ 所有必需的配置项都存在")
        
        # 测试配置在医疗记录生成器中的使用
        print("\n=== 测试在医疗记录生成器中的使用 ===")
        
        # 模拟用户选择 qwen_32B_config
        print("模拟用户选择选项 6 (qwen_32B_config)")
        
        # 验证配置可以被正确导入和使用
        test_config = qwen_32B_config.copy()
        print(f"✅ 配置复制成功: {test_config['model']}")
        
        # 显示配置详情
        print("\n📋 qwen_32B_config 详细配置:")
        for key, value in qwen_32B_config.items():
            if key == 'api_key' and value:
                print(f"  {key}: {value[:10]}...{value[-10:] if len(value) > 20 else value}")
            else:
                print(f"  {key}: {value}")
        
        print("\n=== 与其他配置的对比 ===")
        
        # 导入其他配置进行对比
        from config import qwen_30B_config, deepseek_v3_config, glm_code_config
        
        configs = {
            "qwen_30B": qwen_30B_config,
            "qwen_32B": qwen_32B_config,
            "deepseek_v3": deepseek_v3_config,
            "glm_code": glm_code_config
        }
        
        print("模型对比:")
        for name, config in configs.items():
            model = config.get('model', 'Unknown')
            max_tokens = config.get('default_max_tokens', 'Unknown')
            url = config.get('request_url', 'Unknown')
            print(f"  {name:12}: {model:30} | Max Tokens: {max_tokens:6} | URL: {url}")
        
        print("\n✅ qwen_32B_config 配置测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        return False

def test_model_selection():
    """测试模型选择功能"""
    print("\n" + "=" * 60)
    print("测试模型选择功能")
    print("=" * 60)
    
    try:
        # 模拟医疗记录生成器中的模型选择逻辑
        print("可用的模型配置:")
        print("1. glm_code_config (GLM-4.5-Flash) [推荐]")
        print("2. deepseek_r1_config (DeepSeek R1)")
        print("3. deepseek_v3_config (DeepSeek V3)")
        print("4. kimi_k2_config (Kimi K2)")
        print("5. qwen_30B_config (Qwen 30B)")
        print("6. qwen_32B_config (Qwen 32B)")
        
        # 测试选择 qwen_32B_config
        choice = "6"
        print(f"\n模拟用户选择: {choice}")
        
        if choice == "6":
            from config import qwen_32B_config
            model_config = qwen_32B_config
            print(f"✅ 成功选择 qwen_32B_config")
            print(f"   模型: {model_config['model']}")
            print(f"   URL: {model_config['request_url']}")
        else:
            print("❌ 选择失败")
            return False
        
        print("\n✅ 模型选择功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型选择测试错误: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试 qwen_32B_config")
    
    success1 = test_qwen_32b_config()
    success2 = test_model_selection()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 所有测试通过！")
        print("✅ qwen_32B_config 已成功添加到系统中")
        print("✅ 用户可以在医疗记录生成器中选择使用 qwen_32B_config")
        print("✅ 配置项完整，可以正常使用")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    print(f"\n配置测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"选择测试: {'✅ 通过' if success2 else '❌ 失败'}")
