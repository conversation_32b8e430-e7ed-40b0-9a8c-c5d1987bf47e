1. 请保持对话语言为中文/英文
2. 本项目系统为Windows系统
3. 本项目使用Python语言开发
4. 在生成代码时，不要在同一个文件内生成测试代码，测试代码单独生成在test目录下
5. 在生成代码时，要注意代码的缩进问题，要保持代码的缩进一致
6. 在生成代码时，要注意代码的注释问题，代码注释只需要简洁干净，可以在重要的代码前添加注释，注释要说明代码的功能、参数、返回值，但任何说明需要简洁明了，不需要详细内容
6. 在代码中注意增加logger日志，日志里记录详细的日志信息，包括日志级别、日志内容、日志时间等
7. 代码中的日志信息需要添加在重要的位置，不需要每一步都添加
8. 忽略config和config.py文件，在生成代码时，读取config.json和config.py文件
9. 项目中使用本地环境，python环境为conda，conda环境名称为modelscope
10. 使用cmd，而不是powershell