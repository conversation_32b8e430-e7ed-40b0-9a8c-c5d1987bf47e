# 规则ID: rule_195eff15
# 描述: 检查出院医嘱是否完整包含药名、剂量、用法、带药总量及随访要求

def check_rule(medical_record):
    """
    检查出院医嘱是否完整
    返回True表示存在问题，False表示符合要求
    """
    try:
        # 检查是否包含出院医嘱部分
        if 'discharge_instructions' not in medical_record:
            return True
            
        instructions = medical_record['discharge_instructions']
        
        # 检查必要字段是否存在
        required_fields = [
            'medications',  # 出院带药列表
            'follow_up',    # 随访要求
            'instructions'  # 注意事项
        ]
        
        for field in required_fields:
            if field not in instructions:
                return True
            if not str(instructions[field]).strip():
                return True
        
        # 检查每个药物是否包含必要信息
        medications = instructions['medications']
        for med in medications:
            med_required = [
                'name',        # 药名
                'dosage',      # 剂量
                'usage',       # 用法
                'total_amount' # 带药总量
            ]
            
            for mr in med_required:
                if mr not in med:
                    return True
                if not str(med[mr]).strip():
                    return True
        
        return False
        
    except Exception as e:
        # 任何异常都视为规则不通过
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 缺少必要字段
    test1 = {
        'discharge_instructions': {
            'medications': [],
            'follow_up': '',
            'instructions': '注意事项'
        }
    }
    assert check_rule(test1) == True
    
    # 测试用例2: 药物信息不完整
    test2 = {
        'discharge_instructions': {
            'medications': [{
                'name': '阿司匹林',
                'dosage': '',
                'usage': '口服',
                'total_amount': '10片'
            }],
            'follow_up': '每周复查',
            'instructions': '注意过敏'
        }
    }
    assert check_rule(test2) == True
    
    # 测试用例3: 完整情况
    test3 = {
        'discharge_instructions': {
            'medications': [{
                'name': '阿司匹林',
                'dosage': '100mg',
                'usage': '每日三次',
                'total_amount': '10片'
            }],
            'follow_up': '每周复查血常规',
            'instructions': '避免空腹服用'
        }
    }
    assert check_rule(test3) == False
    
    print("所有测试通过")