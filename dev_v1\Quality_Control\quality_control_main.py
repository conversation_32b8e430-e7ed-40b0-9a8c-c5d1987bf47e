# -*- coding: utf-8 -*-
"""
质控系统总控制文件
根据文档类型调用相应的质控控制器

支持功能：
1. 内涵质控（Content Quality Control）：基于LLM的医疗文档内容质量评估
2. 规则质控（Rule-based Quality Control）：基于预定义规则的合规性检查
3. 综合质控：整合内涵质控和规则质控结果

支持文档类型：
- 出院小结（Discharge Summary）
- 初次病程记录（Initial Progress Note）
"""
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

class QualityControlMain:
    """
    质控系统主控制器

    负责协调和管理不同类型文档的质控检查，包括：
    - 内涵质控：基于AI模型的内容质量评估
    - 规则质控：基于预定义规则的合规性检查
    - 综合质控：整合两种质控结果
    """

    def __init__(self):
        """初始化质控主控制器"""
        self.base_dir = Path(__file__).parent
        self.json_dir = self.base_dir.parent / "rule_type" / "rule_type_json"

        # 文档类型映射表
        self.document_type_mapping = {}
        self._load_document_mappings()

        # 控制器映射
        self.controllers = {}
        self._load_controllers()

        # 支持的质控类型
        self.quality_control_types = {
            "content": "内涵质控",
            "rule": "规则质控",
            "integrated": "综合质控"
        }
    
    def _load_document_mappings(self):
        """
        加载文档类型映射

        从JSON配置文件中加载支持的文档类型映射关系
        """
        try:
            for json_file in self.json_dir.glob("*.json"):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    chinese_name = data['metadata']['document_type_chinese']
                    english_name = data['metadata']['document_type_english']
                    self.document_type_mapping[chinese_name] = english_name
                    print(f"✓ 加载文档类型映射: {chinese_name} -> {english_name}")
        except Exception as e:
            print(f"✗ 加载文档类型映射失败: {e}")

    def _load_controllers(self):
        """
        动态加载控制器

        根据文档类型映射动态加载对应的质控控制器
        """
        for chinese_name, english_name in self.document_type_mapping.items():
            try:
                controller_name = f"{english_name.lower().replace(' ', '_')}_controller"
                controller_module = __import__(controller_name, fromlist=[controller_name])

                # 实例化控制器类
                if english_name == "Discharge Summary":
                    controller_class = getattr(controller_module, 'DischargeSummaryController')
                    self.controllers[chinese_name] = controller_class()
                elif english_name == "Initial Progress Note":
                    controller_class = getattr(controller_module, 'InitialProgressNoteController')
                    self.controllers[chinese_name] = controller_class()
                else:
                    # 通用控制器实例化逻辑
                    class_name = ''.join(word.capitalize() for word in english_name.split()) + 'Controller'
                    controller_class = getattr(controller_module, class_name)
                    self.controllers[chinese_name] = controller_class()

                print(f"✓ 加载控制器: {chinese_name} -> {controller_name}")
            except (ImportError, AttributeError) as e:
                print(f"✗ 无法加载控制器 {controller_name}: {e}")
    
    def run_content_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行内涵质控检查

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 内涵质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_connotation_quality_control(medical_record)

            # 添加元数据
            metadata = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "内涵质控",
                "quality_control_type_english": "Content Quality Control",
                "total_rules": len(result),
                "timestamp": datetime.now().isoformat()
            }

            return {
                "connotation_results": result,
                "metadata": metadata
            }

        except Exception as e:
            return {
                "error": f"内涵质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }

    def run_rule_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行规则质控检查

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 规则质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_regulatory_quality_control(medical_record)

            # 添加元数据
            metadata = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "规则质控",
                "quality_control_type_english": "Rule-based Quality Control",
                "total_rules": len(result),
                "timestamp": datetime.now().isoformat()
            }

            return {
                "regulatory_results": result,
                "metadata": metadata
            }

        except Exception as e:
            return {
                "error": f"规则质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }
    
    def run_integrated_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行综合质控检查（内涵质控 + 规则质控）

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 综合质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_integrated_quality_control(medical_record)

            # 添加元数据
            result["metadata"] = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "综合质控",
                "quality_control_type_english": "Integrated Quality Control",
                "total_rules": len(result.get("regulatory_results", {})) + len(result.get("connotation_results", {})),
                "regulatory_count": len(result.get("regulatory_results", {})),
                "connotation_count": len(result.get("connotation_results", {})),
                "timestamp": datetime.now().isoformat()
            }

            return result

        except Exception as e:
            return {
                "error": f"综合质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }

    # 保持向后兼容性
    def run_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行质控检查（默认为综合质控，保持向后兼容性）

        Args:
            document_type_chinese (str): 中文文档类型
            medical_record (dict): 病历数据

        Returns:
            dict: 综合质控结果
        """
        return self.run_integrated_quality_control(document_type_chinese, medical_record)

    def get_supported_document_types(self) -> dict:
        """
        获取支持的文档类型列表

        Returns:
            dict: 包含支持的文档类型信息
        """
        return {
            "chinese_types": list(self.document_type_mapping.keys()),
            "english_types": list(self.document_type_mapping.values()),
            "mappings": self.document_type_mapping,
            "total_count": len(self.document_type_mapping)
        }

    def get_supported_quality_control_types(self) -> dict:
        """
        获取支持的质控类型列表

        Returns:
            dict: 包含支持的质控类型信息
        """
        return {
            "types": self.quality_control_types,
            "descriptions": {
                "content": "基于AI模型的医疗文档内容质量评估，关注医疗记录的专业性、完整性和准确性",
                "rule": "基于预定义规则的合规性检查，确保文档符合医疗规范和标准",
                "integrated": "综合内涵质控和规则质控的全面质量评估"
            }
        }
    
    def get_quality_control_summary(self, results: dict) -> dict:
        """
        生成质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 质控摘要
        """
        if "error" in results:
            return {"error": results["error"]}

        summary = {
            "document_info": results.get("metadata", {}),
            "overall_score": 0,
            "total_deductions": 0,
            "problem_count": 0,
            "regulatory_summary": {},
            "connotation_summary": {},
            "recommendations": [],
            "quality_grade": "未评估"
        }

        # 统计规则质控结果
        regulatory_results = results.get("regulatory_results", {})
        regulatory_problems = 0
        regulatory_deductions = 0

        for rule, result in regulatory_results.items():
            if result.get("has_problem", False):
                regulatory_problems += 1
                regulatory_deductions += result.get("deduction_points", 0)

        summary["regulatory_summary"] = {
            "total_rules": len(regulatory_results),
            "problem_count": regulatory_problems,
            "total_deductions": regulatory_deductions,
            "compliance_rate": (len(regulatory_results) - regulatory_problems) / len(regulatory_results) * 100 if regulatory_results else 100
        }

        # 统计内涵质控结果
        connotation_results = results.get("connotation_results", {})
        connotation_total_score = 0
        connotation_max_score = 0
        connotation_problems = 0

        for rule, result in connotation_results.items():
            connotation_total_score += result.get("score", 0)
            connotation_max_score += result.get("deduction_points", result.get("max_points", 0))
            if result.get("problems"):
                connotation_problems += 1
                summary["recommendations"].append(result.get("suggestions", ""))

        summary["connotation_summary"] = {
            "total_rules": len(connotation_results),
            "total_score": connotation_total_score,
            "max_score": connotation_max_score,
            "score_rate": connotation_total_score / connotation_max_score * 100 if connotation_max_score > 0 else 100,
            "problem_count": connotation_problems
        }

        # 计算总体评分和等级
        summary["total_deductions"] = regulatory_deductions
        summary["problem_count"] = regulatory_problems + connotation_problems

        # 计算综合得分（基础分100分）
        base_score = 100
        final_score = max(0, base_score - regulatory_deductions + (connotation_total_score - connotation_max_score))
        summary["overall_score"] = final_score

        # 确定质量等级
        if final_score >= 95:
            summary["quality_grade"] = "优秀"
        elif final_score >= 85:
            summary["quality_grade"] = "良好"
        elif final_score >= 75:
            summary["quality_grade"] = "合格"
        elif final_score >= 60:
            summary["quality_grade"] = "需改进"
        else:
            summary["quality_grade"] = "不合格"

        return summary

def main():
    """
    主函数示例

    演示质控系统的各种功能，包括：
    1. 内涵质控
    2. 规则质控
    3. 综合质控
    """
    print("=" * 60)
    print("医疗文档质控系统演示")
    print("=" * 60)

    qc_main = QualityControlMain()

    # 显示支持的文档类型
    supported_types = qc_main.get_supported_document_types()
    print(f"\n📋 支持的文档类型 (共{supported_types['total_count']}种):")
    for chinese, english in supported_types["mappings"].items():
        print(f"  • {chinese} ({english})")

    # 显示支持的质控类型
    qc_types = qc_main.get_supported_quality_control_types()
    print(f"\n🔍 支持的质控类型:")
    for type_key, type_name in qc_types["types"].items():
        description = qc_types["descriptions"][type_key]
        print(f"  • {type_name}: {description}")

    # 测试示例数据
    test_record = {
        "patient_id": "P20240805001",
        "patient_name": "张三",
        "content": """
        患者张三，男，45岁，因"胸痛3天"入院。
        入院诊断：冠心病，心绞痛
        诊疗经过：给予抗血小板聚集、调脂稳斑等治疗
        出院诊断：冠心病，心绞痛（稳定型）
        出院医嘱：阿司匹林100mg qd，阿托伐他汀20mg qn
        出院情况：症状缓解，病情稳定
        """,
        "admission_date": "2024-08-01",
        "discharge_date": "2024-08-05",
        "diagnosis": {
            "admission": "冠心病，心绞痛",
            "discharge": "冠心病，心绞痛（稳定型）"
        },
        "treatment": {
            "medications": ["阿司匹林", "阿托伐他汀"],
            "procedures": []
        }
    }

    # 测试不同类型的质控
    test_cases = [
        ("出院记录", "综合质控"),
        ("出院记录", "内涵质控"),
        ("出院记录", "规则质控"),
        ("首次病程记录", "综合质控")
    ]

    for doc_type, qc_type in test_cases:
        print(f"\n" + "=" * 50)
        print(f"🏥 测试 {doc_type} - {qc_type}")
        print("=" * 50)

        try:
            if qc_type == "综合质控":
                results = qc_main.run_integrated_quality_control(doc_type, test_record)
            elif qc_type == "内涵质控":
                results = qc_main.run_content_quality_control(doc_type, test_record)
            elif qc_type == "规则质控":
                results = qc_main.run_rule_quality_control(doc_type, test_record)

            if "error" in results:
                print(f"❌ 错误: {results['error']}")
            else:
                # 显示基本信息
                metadata = results.get("metadata", {})
                print(f"📄 文档类型: {metadata.get('document_type_chinese')} ({metadata.get('document_type_english')})")
                print(f"🔍 质控类型: {metadata.get('quality_control_type')} ({metadata.get('quality_control_type_english')})")
                print(f"📊 规则总数: {metadata.get('total_rules', 0)}")

                # 生成摘要（仅对综合质控）
                if qc_type == "综合质控":
                    summary = qc_main.get_quality_control_summary(results)
                    print(f"🏆 综合得分: {summary['overall_score']:.1f}/100")
                    print(f"📈 质量等级: {summary['quality_grade']}")
                    print(f"⚠️  问题总数: {summary['problem_count']}")

                    if summary.get("regulatory_summary"):
                        reg_summary = summary["regulatory_summary"]
                        print(f"📋 规则质控: {reg_summary['total_rules']}条规则, {reg_summary['problem_count']}个问题, 合规率{reg_summary['compliance_rate']:.1f}%")

                    if summary.get("connotation_summary"):
                        con_summary = summary["connotation_summary"]
                        print(f"🧠 内涵质控: {con_summary['total_rules']}条规则, 得分率{con_summary['score_rate']:.1f}%")

                print("✅ 质控完成")

        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")

if __name__ == "__main__":
    main()
