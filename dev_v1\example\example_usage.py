#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel转JSON转换器使用示例

本文件展示了如何使用excel_to_json_converter模块来处理医疗质控规则Excel文件
"""

import os
import sys
from excel_to_json_converter import ExcelToJsonConverter


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建转换器实例
    converter = ExcelToJsonConverter()
    
    # Excel文件路径（相对于当前脚本的路径）
    excel_files = [
        "../doc/首次病程录质控.xlsx",
    ]
    
    for excel_file in excel_files:
        if os.path.exists(excel_file):
            print(f"\n处理文件: {excel_file}")
            success = converter.process_excel_file(excel_file)
            
            if success:
                stats = converter.get_statistics()
                print(f"✓ 成功处理 {stats['total_rules']} 条规则")
                print(f"✓ 涉及 {len(stats['document_types'])} 种文档类型")
            else:
                print("✗ 处理失败")
        else:
            print(f"文件不存在: {excel_file}")


def example_analyze_rules():
    """规则分析示例"""
    print("\n=== 规则分析示例 ===")
    
    converter = ExcelToJsonConverter()
    excel_file = "../doc/首次病程录质控.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"文件不存在: {excel_file}")
        return
    
    # 处理文件
    success = converter.process_excel_file(excel_file)
    
    if success:
        # 分析规则数据
        print("\n规则分析结果:")
        
        # 按分类统计
        category_stats = {}
        score_stats = {}
        
        for rule in converter.rules_data:
            # 统计分类
            category = rule.get('category', '未分类')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 统计扣分
            score = rule.get('score', 0)
            score_stats[score] = score_stats.get(score, 0) + 1
        
        print("\n按分类统计:")
        for category, count in sorted(category_stats.items()):
            print(f"  {category}: {count} 条规则")
        
        print("\n按扣分统计:")
        for score, count in sorted(score_stats.items()):
            print(f"  {score}分: {count} 条规则")
        
        # 找出高分规则
        high_score_rules = [rule for rule in converter.rules_data if rule.get('score', 0) >= 6]
        print(f"\n高分规则（≥6分）: {len(high_score_rules)} 条")
        for rule in high_score_rules[:5]:  # 显示前5条
            print(f"  - {rule.get('rule_name', '未命名')}: {rule.get('score', 0)}分")


def main():
    """主函数"""
    print("Excel转JSON转换器使用示例")
    print("=" * 50)
    
    # 检查依赖
    try:
        import pandas as pd
        import openpyxl
        print("✓ 依赖包检查通过")
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 运行示例
    try:
        example_basic_usage()
        example_analyze_rules()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n运行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
