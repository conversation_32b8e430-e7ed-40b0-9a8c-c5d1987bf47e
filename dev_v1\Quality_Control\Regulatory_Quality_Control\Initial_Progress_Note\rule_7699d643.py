# Rule ID: rule_7699d643
# Description: 首次病程缺病例特点

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少病例特点段落
    返回True表示存在问题（缺少），False表示符合要求
    """
    try:
        # 验证输入有效性
        if not isinstance(medical_record, str):
            return False
            
        # 检查是否包含病例特点关键词
        if "病例特点" not in medical_record:
            return True
        return False
            
    except Exception as e:
        print(f"规则执行异常: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试用例1: 包含病例特点
    test1 = "患者男性，50岁。病例特点：发热、咳嗽... 首次病程记录..."
    print(check_rule(test1))  # 预期输出: False
    
    # 测试用例2: 不包含病例特点
    test2 = "患者男性，50岁。首次病程记录..."
    print(check_rule(test2))  # 预期输出: True
    
    # 测试用例3: 异常输入测试
    test3 = 12345
    print(check_rule(test3))  # 预期输出: False