# -*- coding: utf-8 -*-
"""
标准化质控功能测试脚本
测试新的标准化质控接口，处理测试数据集
"""
import json
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from quality_control_main import QualityControlMain

def test_standardized_quality_control():
    """测试标准化质控功能"""
    print("=" * 60)
    print("标准化质控功能测试")
    print("=" * 60)
    
    # 初始化质控系统
    qc_main = QualityControlMain()
    
    # 测试数据路径
    data_path = Path(__file__).parent.parent / "data"
    
    # 测试1: 出院小结数据
    print("\n🏥 测试1: 出院小结数据")
    print("-" * 40)
    
    try:
        discharge_file = data_path / "test_discharge_summary.json"
        if discharge_file.exists():
            with open(discharge_file, 'r', encoding='utf-8') as f:
                discharge_data = json.load(f)
            
            # 测试第一条记录
            test_record = discharge_data['Data'][0]
            print(f"患者: {test_record['Patient']['PatientName']}")
            print(f"科室: {test_record['VisitInfo']['DeptName']}")
            
            # 运行标准化质控
            result = qc_main.run_standardized_quality_control("Discharge Summary", test_record)
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
            else:
                print("✅ 质控完成")
                print_quality_control_result(result)
        else:
            print("❌ 测试数据文件不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    # 测试2: 首次病程记录数据
    print("\n🏥 测试2: 首次病程记录数据")
    print("-" * 40)
    
    try:
        course_file = data_path / "test_first_course_record.json"
        if course_file.exists():
            with open(course_file, 'r', encoding='utf-8') as f:
                course_data = json.load(f)
            
            # 测试第一条记录
            test_record = course_data['Data'][0]
            print(f"患者: {test_record['Patient']['PatientName']}")
            print(f"科室: {test_record['VisitInfo']['DeptName']}")
            print(f"主诉: {test_record['CaseHistoryInfo']['ChiefComplaint']}")
            
            # 运行标准化质控
            result = qc_main.run_standardized_quality_control("Initial Progress Note", test_record)
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
            else:
                print("✅ 质控完成")
                print_quality_control_result(result)
        else:
            print("❌ 测试数据文件不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    # 测试3: 错误处理
    print("\n🏥 测试3: 错误处理")
    print("-" * 40)
    
    # 测试不支持的文档类型
    result = qc_main.run_standardized_quality_control("Unsupported Type", {})
    print(f"不支持的文档类型测试: {'✅ 正确处理' if 'error' in result else '❌ 未正确处理'}")
    
    # 测试无效数据结构
    invalid_data = {"invalid": "data"}
    result = qc_main.run_standardized_quality_control("Discharge Summary", invalid_data)
    print(f"无效数据结构测试: {'✅ 正确处理' if 'error' in result else '❌ 未正确处理'}")

def print_quality_control_result(result):
    """打印质控结果"""
    try:
        # 基本信息
        quality_score = result.get("quality_score", {})
        print(f"🏆 质控分数: {quality_score.get('total_score', 0)}/100")
        print(f"📊 质控状态: {result.get('quality_status', '未知')}")
        print(f"📈 质量等级: {result.get('statistics', {}).get('quality_grade', '未评估')}")
        
        # 统计信息
        stats = result.get("statistics", {})
        print(f"📋 规则总数: {stats.get('total_rules', 0)}")
        print(f"⚠️  问题总数: {stats.get('total_problems', 0)}")
        print(f"🔧 规则质控: {stats.get('regulatory_rules', 0)}条")
        print(f"🧠 内涵质控: {stats.get('connotation_rules', 0)}条")
        
        # 显示前3个问题和建议
        issues = result.get("quality_issues", {})
        suggestions = result.get("quality_suggestions", {})
        
        problem_count = 0
        print("\n📝 主要问题和建议:")
        for rule, issue in issues.items():
            if issue != "无" and problem_count < 3:
                print(f"  • {rule}:")
                print(f"    问题: {issue}")
                print(f"    建议: {suggestions.get(rule, '无')}")
                problem_count += 1
        
        if problem_count == 0:
            print("  🎉 未发现质控问题")
            
    except Exception as e:
        print(f"❌ 结果显示失败: {str(e)}")

def test_data_structure_validation():
    """测试数据结构验证功能"""
    print("\n🔍 测试数据结构验证")
    print("-" * 40)
    
    qc_main = QualityControlMain()
    
    # 测试有效的出院小结数据结构
    valid_discharge_data = {
        "Patient": {
            "PatientId": "TEST001",
            "PatientName": "测试患者",
            "Age": 30,
            "SexName": "男"
        },
        "VisitInfo": {
            "AdmissionNumber": "A001",
            "DeptName": "内科"
        },
        "RepSummaryInfo": {
            "AssayResult": "正常",
            "IsTumor": "否",
            "TreatmentOutcomes": "好转"
        }
    }
    
    is_valid = qc_main._validate_data_structure("Discharge Summary", valid_discharge_data)
    print(f"有效出院小结数据验证: {'✅ 通过' if is_valid else '❌ 失败'}")
    
    # 测试有效的首次病程记录数据结构
    valid_course_data = {
        "Patient": {
            "PatientId": "TEST002",
            "PatientName": "测试患者2",
            "Age": 25,
            "SexName": "女"
        },
        "VisitInfo": {
            "AdmissionNumber": "A002",
            "DeptName": "外科"
        },
        "CaseHistoryInfo": {
            "ChiefComplaint": "头痛3天",
            "Diagnosis": "偏头痛",
            "TreatmentPlan": "对症治疗"
        }
    }
    
    is_valid = qc_main._validate_data_structure("Initial Progress Note", valid_course_data)
    print(f"有效首次病程记录数据验证: {'✅ 通过' if is_valid else '❌ 失败'}")
    
    # 测试无效数据结构
    invalid_data = {"incomplete": "data"}
    is_valid = qc_main._validate_data_structure("Discharge Summary", invalid_data)
    print(f"无效数据结构验证: {'✅ 正确拒绝' if not is_valid else '❌ 错误接受'}")

if __name__ == "__main__":
    test_standardized_quality_control()
    test_data_structure_validation()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
