# 标准化质控API使用指南

## 概述

`quality_control_main.py` 已经升级，新增了 `run_standardized_quality_control` 方法，专门用于处理新创建的测试数据集格式。该方法提供了标准化的质控报告输出，满足项目的具体需求。

## 新增功能

### `run_standardized_quality_control(type_param, data)`

**功能描述**：处理标准化格式的医疗记录数据，返回统一格式的质控报告。

**输入参数**：

1. **type_param** (string)：文档类型参数
   - `"Discharge Summary"` - 处理出院小结数据
   - `"Initial Progress Note"` - 处理首次病程记录数据

2. **data** (dict)：JSON对象，必须严格遵循以下数据结构：

#### 出院小结数据结构
```json
{
  "Patient": {
    "PatientId": "string",
    "PatientName": "string",
    "Age": int,
    "SexName": "string"
  },
  "VisitInfo": {
    "AdmissionNumber": "string",
    "DeptName": "string",
    "AttendingDocName": "string",
    "BedNurName": "string",
    "ChiefDocName": "string",
    "WardName": "string",
    "BedNo": "string",
    "CardNo": "string",
    "InHospitalDateTime": "datetime",
    "VisitNum": int
  },
  "RepSummaryInfo": {
    "AssayResult": "string",
    "ExaminationsAndConsultation": "string",
    "IsTumor": "string",
    "CourseTreatmentOutcomes": "string",
    "Complication": "string",
    "DisHospitalDisease": "string",
    "DisHospitalDrugs": "string",
    "TreatmentOutcomes": "string"
  }
}
```

#### 首次病程记录数据结构
```json
{
  "Patient": {
    "PatientId": "string",
    "PatientName": "string",
    "Age": int,
    "SexName": "string"
  },
  "VisitInfo": {
    "AdmissionNumber": "string",
    "DeptName": "string",
    "WardName": "string",
    "BedNo": "string",
    "CardNo": "string"
  },
  "CaseHistoryInfo": {
    "ChiefComplaint": "string",
    "PhysicalExam": "string",
    "AuxiliaryExam": "string",
    "Diagnosis": "string",
    "Assessment": "string",
    "DifferentialDiagnosis": "string",
    "TreatmentPlan": "string"
  }
}
```

**输出结果**：

返回标准化质控报告，包含以下四个核心字段：

```json
{
  "quality_score": {
    "total_score": 85,
    "base_score": 100,
    "total_deductions": 15,
    "score_details": {
      "规则名称1": "扣分: 5分",
      "规则名称2": "扣分: 0分"
    }
  },
  "quality_status": "通过",
  "quality_issues": {
    "规则名称1": "缺失主要化验结果",
    "规则名称2": "无"
  },
  "quality_suggestions": {
    "规则名称1": "请补充主要化验结果",
    "规则名称2": "无"
  },
  "statistics": {
    "total_rules": 10,
    "regulatory_rules": 6,
    "connotation_rules": 4,
    "total_problems": 2,
    "pass_threshold": 60,
    "quality_grade": "良好"
  },
  "metadata": {
    "document_type_chinese": "出院记录",
    "document_type_english": "Discharge Summary",
    "timestamp": "2025-08-05T10:30:00"
  }
}
```

## 使用示例

### 基本使用

```python
from quality_control_main import QualityControlMain
import json

# 初始化质控系统
qc_main = QualityControlMain()

# 加载测试数据
with open('dev_v1/data/test_discharge_summary.json', 'r', encoding='utf-8') as f:
    test_data = json.load(f)

# 获取第一条记录
record = test_data['Data'][0]

# 运行标准化质控
result = qc_main.run_standardized_quality_control("Discharge Summary", record)

# 检查结果
if "error" in result:
    print(f"质控失败: {result['error']}")
else:
    print(f"质控分数: {result['quality_score']['total_score']}/100")
    print(f"质控状态: {result['quality_status']}")
    print(f"质量等级: {result['statistics']['quality_grade']}")
```

### 批量处理

```python
def batch_quality_control(file_path, doc_type):
    """批量处理质控"""
    qc_main = QualityControlMain()
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = []
    for record in data['Data']:
        result = qc_main.run_standardized_quality_control(doc_type, record)
        results.append({
            "patient_name": record['Patient']['PatientName'],
            "quality_score": result.get('quality_score', {}).get('total_score', 0),
            "quality_status": result.get('quality_status', '未知'),
            "problems": len([v for v in result.get('quality_issues', {}).values() if v != "无"])
        })
    
    return results

# 批量处理出院小结
discharge_results = batch_quality_control(
    'dev_v1/data/test_discharge_summary.json', 
    "Discharge Summary"
)

# 批量处理首次病程记录
course_results = batch_quality_control(
    'dev_v1/data/test_first_course_record.json', 
    "Initial Progress Note"
)
```

### 错误处理

```python
def safe_quality_control(doc_type, record):
    """安全的质控处理，包含错误处理"""
    qc_main = QualityControlMain()
    
    try:
        result = qc_main.run_standardized_quality_control(doc_type, record)
        
        if "error" in result:
            return {
                "success": False,
                "error": result["error"],
                "error_type": "质控执行错误"
            }
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "error_type": "系统异常"
        }
```

## 质控评分标准

### 评分机制
- **基础分数**：100分
- **扣分制**：根据质控规则扣分
- **及格线**：60分
- **状态判定**：≥60分为"通过"，<60分为"不通过"

### 质量等级
- **优秀**：95-100分
- **良好**：85-94分
- **合格**：75-84分
- **需改进**：60-74分
- **不合格**：<60分

### 质控类型
1. **规则质控**：基于预定义规则的合规性检查
   - 问题格式：`"缺失[参数名称]"`
   - 建议格式：`"请补充[参数名称]"`

2. **内涵质控**：基于AI模型的内容质量评估
   - 问题：AI模型输出的具体质控问题
   - 建议：AI模型生成的具体改进建议

## 数据验证

系统会自动验证输入数据的结构完整性：

- ✅ 检查必需的顶层字段（Patient、VisitInfo、RepSummaryInfo/CaseHistoryInfo）
- ✅ 验证Patient字段的完整性（PatientId、PatientName、Age、SexName）
- ✅ 验证VisitInfo字段的完整性（AdmissionNumber、DeptName）
- ✅ 根据文档类型验证特定字段

## 兼容性说明

- ✅ **向后兼容**：保持原有的质控方法不变
- ✅ **新旧并存**：可以同时使用传统质控和标准化质控
- ✅ **数据转换**：自动将新格式转换为控制器可处理的格式

## 测试验证

运行测试脚本验证功能：

```bash
cd dev_v1/Quality_Control
python test_standardized_quality_control.py
```

测试覆盖：
- ✅ 出院小结数据处理
- ✅ 首次病程记录数据处理
- ✅ 数据结构验证
- ✅ 错误处理机制
- ✅ 标准化报告格式

## 注意事项

1. **数据格式**：严格按照指定的JSON结构提供数据
2. **字段命名**：所有字段名称区分大小写，必须完全匹配
3. **必填字段**：确保所有必填字段都有值，空字符串也是有效值
4. **编码格式**：使用UTF-8编码处理中文内容
5. **异常处理**：始终检查返回结果中是否包含"error"字段
