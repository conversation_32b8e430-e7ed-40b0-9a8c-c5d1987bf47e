# -*- coding: utf-8 -*-
"""
出院记录内涵质控主控制文件
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from model_use import llm_use
from config import qwen_32B_config

def process_discharge_medication_connotation(medical_record):
    """处理出院医嘱内涵质控"""
    try:
        # 读取prompt文件
        prompt_file = Path(__file__).parent / "discharge_summary_prompt.json"
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt_data = json.load(f)
        
        # 构建用户prompt
        user_prompt = prompt_data["user_prompt_template"].format(
            medical_record_content=medical_record.get('content', '')
        )
        
        # 调用大模型
        response = llm_use(prompt_data["system_prompt"], user_prompt, qwen_32B_config)
        
        if response:
            # 解析响应（简化版本）
            return {
                'score': 25,  # 默认分数
                'problems': '需要检查出院医嘱的完整性',
                'suggestions': '建议完善出院医嘱，包括药名、剂量、用法等信息'
            }
        else:
            return {
                'score': 0,
                'problems': '大模型调用失败',
                'suggestions': '请检查模型配置'
            }
    except Exception as e:
        return {
            'score': 0,
            'problems': f'内涵质控处理失败: {str(e)}',
            'suggestions': '请检查系统配置'
        }

def run_connotation_quality_control(medical_record):
    """运行所有内涵质控检查"""
    results = {}
    
    # 出院医嘱内涵质控
    results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
        'rule_type': '段落完整性',
        'classification': '手术科室',
        'max_points': 30.0,
        'score': 0,
        'problems': '',
        'suggestions': '',
        'type': 'connotation'
    }
    
    # 调用内涵质控处理
    connotation_result = process_discharge_medication_connotation(medical_record)
    results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'].update(connotation_result)
    
    return results

if __name__ == "__main__":
    test_record = {
        "content": "测试出院记录内容，包含出院医嘱信息"
    }
    
    results = run_connotation_quality_control(test_record)
    print("出院记录内涵质控结果:")
    for rule, result in results.items():
        print(f"- {rule}: 得分{result['score']}/{result['max_points']}")
